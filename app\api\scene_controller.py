import json
from typing import Dict, Any, List
from typing import Optional

from fastapi import APIRouter, Depends, Body, Query
from sqlalchemy.orm import Session

from app.common.response_com import create_response
from app.db.database import get_db  # 修改导入路径
from app.log.log_utils import LogUtils
from app.models.scene_2_po import Scene2PO
from app.models.scene_5_record import Scene5OcrPO, Scene5Record
from app.models.situation_analysis_record import SituationAnalysisRecord
from app.schemas.situation_analysis_schema import UpdateReportRequest
from app.services.dataset_service import get_dataset_service
from app.services.db_service import DatabaseService
from app.services.nl2sql_service import Nl2sqlService
from app.services.scene_3_service import Scene_3_Service
from app.services.situation_analysis_service import save_report

from app.models.police_record import PoliceRecord
from app.models.qz_case_feature import QzCaseFeature

router = APIRouter()

# 创建全局线程池执行器
from concurrent.futures import ThreadPoolExecutor

executor = ThreadPoolExecutor()


@router.post("/sql_execute")
async def sql_execute(
        sql: str,
        db: Session = Depends(get_db)
):
    result = Nl2sqlService.sql_execute(sql, db)
    return create_response(data=result)


@router.post("/update", description="更新执法分析报告")
async def update_report_md(
        request: UpdateReportRequest = Body(...),
        db: Session = Depends(get_db),
):
    # 1. 查询记录
    record = db.query(SituationAnalysisRecord).filter_by(id=request.report_id).first()
    if not record:
        raise create_response(code=500, message="报告记录不存在")

    # 2. 更新内容和状态
    sorted_items = sorted(
        request.content.items(),
        key=lambda x: [int(num) for num in x[0].split('_')[1:]]
    )

    # 新增清洗逻辑
    cleaned_values = []
    for k, v in sorted_items:
        if v:
            # 转换为字符串并清洗
            str_value = str(v).strip()

            if str_value.startswith('```markdown'):
                str_value = str_value.replace('```markdown', '', 1).rstrip('```').strip()
            cleaned_values.append(str_value)

    # 拼接逻辑保持不变
    formatted_content = '\n'.join(cleaned_values)  # 修改拼接逻辑
    record.md_content = formatted_content
    record.status = 1  # 已完成状态

    # 3. 保存到数据库
    try:
        saved_record = save_report(db, record)
    except Exception as e:
        raise create_response(code=500, message=f"保存失败: {str(e)}")

    return create_response(data={
        "report_id": saved_record.id,
        "status": saved_record.status,
    }, message="报告内容已保存并完成")


@router.post("/scene_4/getDetailById")
async def search_records(
        id: Optional[int] = None,
        db: Session = Depends(get_db)
):
    # 使用 DatabaseService 构建查询
    record = DatabaseService.check_tmp_transcript_exists_by_id(db, id)

    if not record:
        LogUtils.info(f"该数据不存在")
        return create_response(
            message="该数据不存在",
            code=500
        )
    record.blnr = ''.join(record.blnr.split())
    return create_response(
        message="该PDF文件已存在",
        data=record
    )


@router.post("/scene_4/update_analysis_result")
async def save_transcript(
        data: dict,
        db: Session = Depends(get_db)
):
    """
    保存笔录数据到数据库
    """
    try:
        # 检查数据中是否包含md_id
        id = data.get('id')
        if not id:
            return create_response(
                code=500,
                message="缺少必要的id字段"
            )

        # 检查记录是否已存在
        record = DatabaseService.check_tmp_transcript_exists_by_id(db, id)
        if not record:
            return create_response(
                code=500,
                message="未找到对应的记录"
            )
        record.detection_result = data.get('detection_result')
        record.analysis_status = 2
        record.check_status = data.get('check_status')
        db.commit()
        LogUtils.info(f"笔录数据更新成功,id:{id}")
        message = "笔录数据更新成功"

        return create_response(
            message=message,
            data=record
        )
    except Exception as e:
        LogUtils.error(f"数据保存异常: {str(e)}")
        record.analysis_status = 3
        db.commit()
        # db.rollback()
        return create_response(
            code=500,
            message=f"数据保存失败"
        )


@router.post("/scene_3/legal/getDetailById")
async def search_records(
        jlbh: Optional[str] = None,
        db: Session = Depends(get_db)
):
    # 使用 DatabaseService 构建查询
    record = DatabaseService.check_legal_record_exists(db, jlbh)
    if not record:
        LogUtils.info(f"该数据不存在")
        return create_response(
            message="该数据不存在",
            code=500
        )
    data = Scene_3_Service.cover_record_exists(record)
    return create_response(
        message="success",
        data=data
    )


@router.post("/scene_3/save_legal")
async def search_records(
        data: dict,
        db: Session = Depends(get_db)
):
    """
    保存笔录数据到数据库
    """
    try:
        # 检查数据中是否包含md_id
        jlbh = data.get('jlbh')
        if not jlbh:
            return create_response(
                code=500,
                message="缺少必要的jlbh字段"
            )

        # 检查记录是否已存在
        record = DatabaseService.check_legal_record_exists(db, jlbh)
        if not record:
            return create_response(
                code=500,
                message="未找到对应的记录"
            )
        record.detection_result = data.get('detection_result')
        # 0未分析，1分析中，2分析完成，3分析失败
        record.analysis_status = 2
        record.check_status = data.get('check_status')
        db.commit()
        LogUtils.info(f"数据已更新: {jlbh}")
        message = "法律文书数据更新成功"

        return create_response(
            message=message,
            data=record
        )
    except Exception as e:
        # 0未分析，1分析中，2分析完成，3分析失败
        record.analysis_status = 3
        db.commit()
        LogUtils.error(f"数据保存异常: {str(e)}")
        return create_response(
            code=500,
            message=f"数据保存失败"
        )


"""
第二个场景：立而不侦sql组装接口
"""


@router.get("/scene_2/get_summary")
async def get_summary(
        ajbh: str,
        db: Session = Depends(get_db)
) -> Any | None:
    """
    立而不侦场景SQL查询接口

    Args:
        ajbh: 案件编号
        current_user: 当前用户信息

    Returns:
        Dict[str, Any]: 查询结果
    """
    # 查询时间范围内的案件
    record = db.query(Scene2PO).filter(
        Scene2PO.ajbh == ajbh,
    ).first()
    if record:
        return create_response(
            message='查询成功',
            data=record.summary
        )
    return create_response(
        message='查询失败',
        data=None
    )


@router.post("/scene_2/update_analysis_result/{ajbh}")
async def update_analysis_result(
        ajbh: str,
        conclusion: str = Body(..., embed=True),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    立而不侦场景SQL查询接口

    Args:
        ajbh: 案件编号
        current_user: 当前用户信息

    Returns:
        Dict[str, Any]: 查询结果
    """

    # 查询时间范围内的案件
    record = db.query(Scene2PO).filter(
        Scene2PO.ajbh == ajbh,
    ).first()
    if record:
        conclusion_dict = json.loads(conclusion)

        record.analysis_result = conclusion_dict['result']
        record.analysis_tag = conclusion_dict['tag']
        record.analysis_status = 'completed'
        db.add(record)
        db.commit()
        return create_response(
            message='分析结果更新成功',
        )
    return create_response(
        message='未找到待分析原始记录',
    )


@router.get("/scene_5/get_ocr_list")
async def get_ocr_list(
        id: int,
        db: Session = Depends(get_db)
) -> Any | None:
    LogUtils.info(f"卷宗分析 开始: {id} ")
    aj_record = db.query(Scene5Record).filter(
        Scene5Record.id == id,
    ).first()
    if not aj_record:
        return create_response(
            message='案件不存在',
            data=None
        )

    # 查询时间范围内的案件
    query = db.query(Scene5OcrPO).filter(
        Scene5OcrPO.scene_5_record_id == id,
    )
    query = query.order_by(Scene5OcrPO.sn.asc())

    records = query.all()

    if records:
        # 收集 records 中的 ocr_content，汇总为数组返回
        ocr_contents = [record.ocr_content for record in records if record.ocr_content]
        result = {
            "ocr_page_content": ocr_contents,
            "aj_type": aj_record.aj_type
        }
        return create_response(
            message='查询成功',
            data=result
        )

    return create_response(
        message='查询失败',
        data={
            "ocr_page_content": [],
            "aj_type": aj_record.aj_type
        }
    )


@router.post("/scene_5/update_analysis_result")
async def update_analysis_result(
        data: dict,
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    # 查询时间范围内的案件
    # 检查数据中是否包含md_id
    id = data.get('id')
    LogUtils.info(f"卷宗分析接口保存 : {id} ")
    record = db.query(Scene5Record).filter(
        Scene5Record.id == id,
    ).first()
    if record:
        if record.source == 1:
            if data.get('hoster_name'):
                record.hoster_name = data['hoster_name']
            if data.get('case_code'):
                record.case_code = data['case_code']
            if data.get('case_name'):
                record.case_name = data['case_name']
        if data.get('catalogs'):
            record.analysis_page = data['catalogs']
        if data.get('analysis_error'):
            record.analysis_error = data['analysis_error']
        record.analysis_result = 2
        db.add(record)
        db.commit()
        return create_response(
            message='分析结果更新成功',
        )
    return create_response(
        message='未找到待分析原始记录',
    )


@router.get("/dataset/getSplitText")
async def getSplitText(
        md5: str
) -> Dict[str, Any]:

    if md5:
        dataset_service = await get_dataset_service()
        texts:list[str]=await dataset_service.getSplitText(md5)
        return create_response(
            message='试题集转换QA结果成功',
            data={
               "texts": texts
            }
        )
    return create_response(
        message='查询失败',
        data=None
    )



@router.post("/dataset/update_dataset_result")
async def update_dataset_result(
        data: list[dict]
) -> Dict[str, Any]:
    if data:
        # LogUtils.info(f"收到转换的qa结果是：{data}")
        dataset_service = await get_dataset_service()
        await dataset_service.update_dataset_result(data)
        return create_response(
            message='试题集转换QA结果成功',
        )
    return create_response(
        message='未接收到试题集啊转QA记录',
    )

@router.get("/scene_qz/get_alarm_details")
async def get_alarm_details(
        ajbh: str,
        db: Session = Depends(get_db)
) -> Any | None:
    try:
        aj_record = db.query(PoliceRecord).filter(
            PoliceRecord.police_number == ajbh,
        ).first()

        if not aj_record:
            return create_response(
                message='案件不存在',
                data=None
            )

        result = {
            "alarm_details": aj_record.alarm_details,

        }
        return create_response(
            message='查询成功',
            data=result
        )
    except Exception as e:
        return create_response(
            message=f'查询失败{str(e)}',
            data=None
        )

@router.post("/scene_qz/add_new_data")
async def update_scene8_result(
        data: List[Dict],
        ajbh: str = Query(...),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    try:
        for item in data:

            record = QzCaseFeature(
                case_id=ajbh,
                entity_id=item.get("entity_id"),
                feature_type=item.get("feature_type"),
                feature_value=item.get("feature_value"),
            )
            db.add(record)

        db.commit()
        return create_response(
            message='数据入库成功',
        )
    except Exception as e:
        LogUtils.error(f"入库过程出错: {str(e)}")
        return create_response(
            message='入库异常',
        )



@router.post("/dataset/update_dataset_reasoning")
async def update_dataset_reasoning(
        data: dict
) -> Dict[str, Any]:
    if data:
        # LogUtils.info(f"收到转换的qa结果是：{data}")
        dataset_service = await get_dataset_service()
        await dataset_service.update_dataset_reasoning(data)
        return create_response(
            message='QA获取reasoning_content成功',
        )
    return create_response(
        message='未接收到reasoning_content',
    )

