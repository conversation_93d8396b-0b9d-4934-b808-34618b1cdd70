<template>
  <div class="sidebar">
    <div class="sidebar-header">
      <div class="logo-img">
        <img :src="logo" alt="" />
      </div>
      <div class="logo">{{ title }}</div>
      <div class="tip">{{ tipTitle }}</div>
      <div class="line">
        <img src="@/assets/images/ai/line.svg" alt="" />
      </div>
    </div>
    <!-- <div class="title-label">能力特点</div> -->
    <div class="ability-list">
      <div class="ability-item" v-for="item in abilityList" :key="item.title">
        <div class="ability-item-icon">
          <img :src="item.icon" :alt="item.title" />
        </div>
        <div class="ability-item-content">
          <div class="ability-item-content-title">{{ item.title }}</div>
          <div class="ability-item-content-tip">{{ item.tip }}</div>
        </div>
      </div>
    </div>
    <div class="title-label">会话记录</div>
    <div class="sidebar-content" ref="sidebarContent" @scroll="handleScroll">
      <div v-if="conversations.length === 0" class="empty">暂无历史对话</div>
      <div v-else>
        <div v-for="group in groupedConversations" :key="group.title">
          <div class="history-group-title">{{ group.title }}</div>
          <div
            v-for="conv in group.items"
            :key="conv.id"
            class="history-item"
            :class="{ active: conv.id === currentConversationId }"
          >
            <div class="history-item-content" @click="handleSelect(conv.id)">
              <span @dblclick.stop="startRename(conv)">
                <template v-if="renameId === conv.id">
                  <el-input
                    v-model="renameName"
                    size="mini"
                    @blur="confirmRename(conv)"
                    @keyup.enter.native="confirmRename(conv)"
                    style="width: 100%"
                  />
                </template>
                <template v-else>
                  {{ conv.name }}
                </template>
              </span>
            </div>
            <el-dropdown trigger="click" @command="handleCommand($event, conv)">
              <span class="item-actions" @click.stop>
                <i class="el-icon-more"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="rename">重命名</el-dropdown-item>
                <!-- <el-dropdown-item command="delete" divided>删除</el-dropdown-item> -->
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      </div>
      <div v-if="loading" class="loading-more">
        <i class="el-icon-loading"></i>
        加载中...
      </div>
    </div>
  </div>
</template>

<script>
import { deleteConversation, renameConversation, update_conversation_name } from "@/api/chat";
import language from "@/assets/images/ai/language.svg";
import search from "@/assets/images/ai/search.svg";
import doc from "@/assets/images/ai/doc.svg";
import help from "@/assets/images/ai/help.svg";
import logo from "@/assets/images/ai/logo.svg";
// 模拟分组数据方法
function groupConversations(conversations) {
  const groups = {};
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);

  conversations.forEach(conv => {
    // 将时间戳转换为日期对象
    const date = new Date(conv.updated_at * 1000);
    const dateStart = new Date(date);
    dateStart.setHours(0, 0, 0, 0);

    let groupKey;
    // 判断是否是今天
    if (dateStart.getTime() === today.getTime()) {
      groupKey = "今天";
    }
    // 判断是否是昨天
    else if (dateStart.getTime() === yesterday.getTime()) {
      groupKey = "昨天";
    }
    // 其他日期按年月分组
    else {
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      groupKey = `${year}-${month.toString().padStart(2, "0")}`;
    }

    // 如果分组不存在，创建新分组
    if (!groups[groupKey]) {
      groups[groupKey] = [];
    }

    // 将会话添加到对应分组
    groups[groupKey].push(conv);
  });

  // 将分组转换为数组并排序
  return Object.entries(groups)
    .map(([title, items]) => ({ title, items }))
    .sort((a, b) => {
      // 特殊处理"今天"和"昨天"
      if (a.title === "今天") return -1;
      if (b.title === "今天") return 1;
      if (a.title === "昨天") return -1;
      if (b.title === "昨天") return 1;

      // 其他按时间倒序排列
      const [yearA, monthA] = a.title.split("-").map(Number);
      const [yearB, monthB] = b.title.split("-").map(Number);
      if (yearA !== yearB) return yearB - yearA;
      return monthB - monthA;
    });
}

export default {
  props: {
    title: {
      type: String,
      default: "AI智能助手",
    },
    namespace: {
      type: String,
      required: true,
    },
    tipTitle: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      renameId: null,
      renameName: "",
      api_key: "app-pVSwMmjEc4gmBIFGXrlsUFne",
      loading: false,
      hasMore: false,
      lastId: null,
      abilityList: [
        {
          icon: language,
          title: "自然语言理解",
          tip: "能够理解复杂指令和上下文，提供连贯对话。",
        },
        {
          icon: search,
          title: "信息检索",
          tip: "快速从系统数据库查找相关信息和资料",
        },
        {
          icon: doc,
          title: "文档处理",
          tip: "协助创建，编辑和分析各类文档内容",
        },
        {
          icon: help,
          title: "任务帮助",
          tip: "提供工作流程指导，跟踪任务进度",
        },
      ],
      logo,
    };
  },
  computed: {
    conversations() {
      return this.$store.getters[`${this.namespace}/conversations`];
    },
    currentConversationId() {
      return this.$store.getters[`${this.namespace}/currentConversationId`];
    },
    error() {
      return this.$store.getters[`${this.namespace}/error`];
    },
    groupedConversations() {
      return groupConversations(this.conversations);
    },
    agent_id() {
      return this.$store.getters[`${this.namespace}/agent_id`];
    },
  },
  watch: {
    currentConversationId(val) {
      console.log("currentConversationId changed:", val);
    },
  },
  methods: {
    async createConversation() {
      return this.$store.dispatch(`${this.namespace}/createConversation`);
    },
    async deleteConversation(id) {
      return this.$store.dispatch(`${this.namespace}/deleteConversation`, id);
    },
    async renameConversation(data) {
      return this.$store.dispatch(`${this.namespace}/renameConversation`, data);
    },
    async setCurrentConversation(id) {
      return this.$store.dispatch(`${this.namespace}/setCurrentConversation`, id);
    },
    async fetchConversations(params) {
      return this.$store.dispatch(`${this.namespace}/fetchConversations`, params);
    },
    async fetchConversationHistory(params) {
      return this.$store.dispatch(`${this.namespace}/fetchConversationHistory`, params);
    },
    async addConversation() {
      // 只切换到欢迎页，不弹窗
      this.$store.commit(`${this.namespace}/SET_CURRENT_CONVERSATION`, null);
    },
    async remove(id) {
      try {
        await this.$confirm("确定要删除该对话吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });

        const response = await deleteConversation(id, "abc-123");
        if (response.status === 204 || response.ok) {
          // 如果删除的是当前选中的会话，重置当前会话ID
          if (id === this.currentConversationId) {
            await this.setCurrentConversation(null);
          }
          // 重新获取会话列表
          await this.fetchConversations({ append: false });
          this.$message.success("删除成功");
        } else {
          throw new Error("删除失败");
        }
      } catch (error) {}
    },
    startRename(conv) {
      this.renameId = conv.id;
      this.renameName = conv.name;
      this.$nextTick(() => {
        this.$el.querySelector("input").focus();
      });
    },
    async confirmRename(conv) {
      if (this.renameName && this.renameName !== conv.name) {
        try {
          const response = await update_conversation_name(this.agent_id, conv.id, {
            name: this.renameName,
          });
          if (response.code !== 200) {
            throw new Error("重命名失败");
          }

          const data = response.data;
          await this.renameConversation({ id: conv.id, name: data.name });
          this.$message.success("重命名成功");
        } catch (error) {
          this.$message.error(error.message || "重命名失败");
        }
      }
      this.renameId = null;
      this.renameName = "";
    },
    handleSelect(id) {
      // 获取历史消息并切换会话
      // const user = "abc-123";
      // const api_key = "你的API_KEY"; // TODO: 替换为你的真实API KEY
      this.fetchConversationHistory({ conversation_id: id, append: false });
    },
    handleCommand(command, conv) {
      if (command === "rename") {
        this.startRename(conv);
      } else if (command === "delete") {
        this.remove(conv.id);
      }
    },
    async handleScroll(e) {
      const { scrollTop, scrollHeight, clientHeight } = e.target;
      // 当滚动到距离底部20px时触发加载
      if (scrollHeight - scrollTop - clientHeight < 20 && !this.loading && this.hasMore) {
        await this.loadMore();
      }
    },
    async loadMore() {
      if (this.loading || !this.hasMore) return;

      this.loading = true;
      try {
        // 保存当前会话列表的长度，用于后续判断是否有新数据
        const currentLength = this.conversations.length;

        await this.fetchConversations({
          last_id: this.lastId,
          append: true, // 添加append参数，告诉store需要追加数据
        });

        // 从store中获取最新状态
        const conversations = this.conversations;
        if (conversations && conversations.length > currentLength) {
          // 更新lastId为新数据的最后一条
          this.lastId = conversations[conversations.length - 1].id;
          // 更新hasMore状态
          this.hasMore = this.$store.state[this.namespace].hasMore;
        }
      } catch (error) {
      } finally {
        this.loading = false;
      }
    },
  },
  created() {
    // 只切换到欢迎页，不弹窗
    this.$store.commit(`${this.namespace}/SET_CURRENT_CONVERSATION`, null);

    // 初始化加载
    this.fetchConversations({ append: false })
      .then(() => {
        const conversations = this.conversations;
        if (conversations && conversations.length > 0) {
          this.lastId = conversations[conversations.length - 1].id;
          this.hasMore = this.$store.state[this.namespace].hasMore;
        }
      })
      .catch(error => {
        console.error("加载会话列表失败:", error);
      });
  },
};
</script>

<style scoped lang="less">
.sidebar {
  width: 320px;
  /* background: #f9fbff; */
  display: flex;
  flex-direction: column;
  height: 100vh;
  position: relative;
  box-shadow: none;
  // padding-left: 20px;
  // padding-right: 20px;
}
.sidebar-header {
  padding: 20px 20px 12px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  .logo-img {
    width: 50px;
    height: 50px;
    margin-bottom: 12px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .logo {
    font-weight: 600;
    font-size: 18px;
    letter-spacing: 1px;
    margin-bottom: 5px;
    color: #fff;
    text-shadow: none;
  }
  .tip {
    color: #909399;
    font-size: 14px;
  }
  .line {
    width: 50%;
    img {
      width: 100%;
    }
  }
}
.title-label {
  color: #909399;
  // border-bottom: 1px solid #0f2336;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-left: 20px;
  margin-top: 10px;
  padding-bottom: 5px;
}
.ability-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 10px;
  border-bottom: 1px solid #06445d;
  padding-left: 20px;
  padding-right: 20px;
  padding-bottom: 20px;
  box-shadow: 0 2px 5px 0px #06445d;
  .ability-item {
    display: flex;
    align-items: center;
    flex-direction: row;
    gap: 10px;
  }
  .ability-item-icon {
    width: 30px;
    height: 30px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .ability-item-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
    .ability-item-content-title {
      font-size: 13px;
      // font-weight: 600;
    }
    .ability-item-content-tip {
      font-size: 12px;
      color: #909399;
    }
  }
}

.new-chat-btn {
  /* width: 100%; */
  height: 36px;
  border-radius: 8px;
  padding: 0px 10px;
  transition: transform 0.2s, background 0.2s;
  background: rgba(0, 255, 255, 0.2);
  color: #00ffff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  border: none;
  font-weight: 500;
  box-shadow: none;
}

.new-chat-btn:hover {
  transform: scale(1.05);
}

.new-chat-btn span {
  margin-left: 6px;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;

  padding-bottom: 120px;

  /* Firefox */
  scrollbar-width: none;
  scrollbar-color: #888 #f1f1f1;

  /* WebKit */
  // &::-webkit-scrollbar {
  //   width: 8px;
  //   height: 8px;
  // }
  // &::-webkit-scrollbar-track {
  //   background: #f1f1f1;
  // }
  // &::-webkit-scrollbar-thumb {
  //   background: #888;
  //   border-radius: 4px;
  // }
  // 隐藏滚动条
  &::-webkit-scrollbar {
    display: none;
    width: 0;
  }
}

.empty {
  text-align: center;
  padding: 20px 0;
  color: #909399;
  font-size: 13px;
}
.history-group-title {
  color: #909399;
  font-size: 13px;
  margin: 24px 0 8px 20px;
}
.history-item {
  padding: 8px 20px;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.2s;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 2px 8px;
  color: rgba(255, 255, 255, 0.7);
}
.history-item.active,
.history-item:hover {
  background: rgba(0, 255, 255, 0.2);
}
.item-actions {
  margin-left: 8px;
  opacity: 0;
  transition: opacity 0.2s;
}
.history-item:hover .item-actions {
  opacity: 1;
}
.item-actions i {
  font-size: 16px;
  color: #909399;
  padding: 4px;
}
.item-actions i:hover {
  color: #00ffff;
}

.history-item-content {
  flex: 1;
  display: flex;
  align-items: center;

  /* 新增部分 */
  min-width: 0; /* 修复 flex 容器溢出问题 */
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.loading-more {
  text-align: center;
  padding: 10px 0;
  color: #909399;
  font-size: 13px;
}
</style>
