<template>
  <div class="chat-header-wrapper">
    <div class="header-content">AI智能助手 · {{ title }}</div>
    <!-- <div class="welcome-title">{{ welcomeTitle }}</div> -->
  </div>
</template>
<script>
export default {
  props: {
    title: {
      type: String,
      default: "AI智能助手",
    },
    welcomeTitle: {
      type: String,
      default: "欢迎使用AI智能助手，我是你的助手，我可以为您提供帮助。",
    },
  },
};
</script>
<style scoped lang="less">
.chat-header-wrapper {
  width: 100%;
  padding: 15px 20px;
  background-color: #f8f8f8;
  display: flex;
  gap: 10px;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid #06445d;
  background-color: #08172e;
  box-shadow: #06445d 0px 0px 10px 0px;
  position: relative;
  .header-content {
    font-size: 25px;
    // font-weight: bold;
    color: #ffffff;
  }
  .welcome-title {
    position: absolute;
    bottom: -100%;
    // border: 1px solid #fff;
    border-radius: 20px;
    width: 400px;
    margin: 0 auto;
    text-align: center;
    // margin-top: 350px;
    font-size: 18px;
    padding: 5px;
    color: #fff;
  }
}
</style>
