"""
聊天智能体核心动作实现
"""
import time
from typing import Dict, <PERSON>, <PERSON><PERSON>, AsyncIterable

from sqlalchemy.orm import Session

from app.log.log_utils import LogUtils
from app.models.chat import Dataset, DocFeature
from app.services.chat_agent.actions.base import Action
from app.services.chat_agent.base import ActionResult
from app.services.chat_agent.handlers.interfaces import KnowledgeBaseHandler, QueryProcessingHandler

def _get_existing_features( dataset_id: int,db : Session) -> Dict:
    """获取现有特征数据"""
    #id倒序最新的一个风格特征值
    features = db.query(DocFeature).filter(
        DocFeature.local_dataset_id == dataset_id
    ).order_by(DocFeature.id.desc()).first()  # 添加倒序排序

    return {
        "structure": features.structure_feature if features else "",
        "style": features.writing_style_feature if features else ""
    }

def get_latest_document_data(agent_id: str,user:str, db: Session):
    """
    联合查询Dataset和DocFeature表，获取最新文档的writing_style_feature和dataset_id
    
    Args:
        local_dataset_id: 本地数据集ID
        db: 数据库会话
        
    Returns:
        包含writing_style_feature和dataset_id的字典
    """
    # 联合查询Dataset和DocFeature表
    result = db.query(
        DocFeature.writing_style_feature,
        Dataset.dataset_id
    ).join(
        Dataset,
        DocFeature.local_dataset_id == Dataset.id
    ).filter(
        Dataset.agent_id == agent_id,
        Dataset.user == user,
        # 过滤掉空的写作风格特征
        DocFeature.writing_style_feature != "",
    ).order_by(
        DocFeature.id.desc()  # 按ID降序排序
    ).first()

    if result and len(result) >= 2:
        # 提取查询结果中的writing_style_feature和dataset_id
        writing_style_feature = result[0] or ""  # 如果为None则返回空JSON对象
        dataset_id = result[1]
        return {
            "writing_style_feature":writing_style_feature ,
            "dataset_id":dataset_id
        }
    return {}

class SecretarialFeaturesAction(Action):
    """
    文秘办公，个人知识库 文档检索动作
    """

    def __init__(self, kb_handler: KnowledgeBaseHandler,db = Session):
        self.kb_handler = kb_handler
        self.db = db

    async def execute(self, context: Dict[str, Any]) -> Tuple[ActionResult, Dict[str, Any]]:
        query = context.get("query", {})
        
        try:
            LogUtils.info(f"执行文档检索动作: {query}")
            start_time = time.time()
            
            # 获取最新文档数据
            doc_data = get_latest_document_data(context.get("agent_id"),context.get("user_profile",{}).get("id"), self.db)
            dataset_id = doc_data.get("dataset_id","")

            # 查询结构特征
            structure_feature = await self.kb_handler.search_documents(dataset_id, query.get("message"))
            
            # 获取写作风格特征
            writing_style_feature = doc_data.get("writing_style_feature","")
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            # 更新context
            context["chatflow_params"] = {
                "structure_feature": structure_feature,
                "writing_style_feature": writing_style_feature
            }
            context["processing_time"] = context.get("processing_time", 0) + processing_time
            
            LogUtils.info(f"文档检索完成，耗时 {processing_time:.2f}s")
            return ActionResult.CONTINUE, context
        except Exception as e:
            LogUtils.error(f"文档检索失败: {str(e)}")
            context["error"] = f"文档检索失败: {str(e)}"
            return ActionResult.FAILURE, context


class TextGenerationAction(Action):
    """文本生成动作"""

    def __init__(self, query_processor: QueryProcessingHandler):
        self.query_processor = query_processor

    async def execute(self, context: Dict[str, Any]) -> Tuple[ActionResult, Dict[str, Any]]:
        query = context.get("query", "")
        retrieved_docs = context.get("retrieved_documents", "")
        user_profile = context.get("user_profile", {})
        agent_config = context.get("agent_config", {})
        conversation_id = context.get("conversation_id", "")

        try:
            LogUtils.info(f"执行文本生成动作: {query}")
            start_time = time.time()

            answer = await self.query_processor.generate_answer(
                query, retrieved_docs, user_profile, agent_config
            )

            end_time = time.time()
            processing_time = end_time - start_time

            context["generated_answer"] = answer
            context["processing_time"] = context.get("processing_time", 0) + processing_time

            LogUtils.info(f"文本生成完成，耗时 {processing_time:.2f}s")
            return ActionResult.SUCCESS, context
        except Exception as e:
            LogUtils.error(f"文本生成失败: {str(e)}")
            context["error"] = f"文本生成失败: {str(e)}"
            return ActionResult.FAILURE, context

    async def execute_stream(self, context: Dict[str, Any]) -> AsyncIterable[Dict[str, Any]]:
        """
        流式执行文本生成
        
        Args:
            context: 上下文信息
            
        Yields:
            生成过程中的数据块
        """
        query_dict = context.get("query", {})  # 获取完整的查询字典
        retrieved_docs = context.get("retrieved_documents", "")
        user_profile = context.get("user_profile", {})
        agent_config = context.get("agent_config", {})

        try:
            LogUtils.info(f"流式执行文本生成动作: {query_dict.get('message', '')}")
            start_time = time.time()

            # 调用query_processor的流式生成方法，传递整个query字典
            async for chunk in self.query_processor.generate_answer_stream(
                    query=query_dict,  # 传递整个查询字典
                    global_context=context,
                    dataset_context=retrieved_docs,
                    user_profile=user_profile,
                    agent_config=agent_config
            ):
                yield chunk
                # if "text_chunk" in chunk:
                #     # 传递文本块
                #     yield {"text_chunk": chunk["text_chunk"]}
                # elif "citation" in chunk:
                #     # 传递引用信息
                #     yield {"citation": chunk["citation"]}
                # elif "error" in chunk:
                #     # 传递错误信息
                #     yield {"error": chunk["error"]}
                #     return

            # 生成结束时发送包含元数据的结束信号
            end_time = time.time()
            processing_time = end_time - start_time
            yield {
                "end": True,
                "metadata": {
                    "sources": retrieved_docs,
                    "processing_time": processing_time
                }
            }

            LogUtils.info(f"流式文本生成完成，耗时 {processing_time:.2f}s")
        except Exception as e:
            LogUtils.error(f"流式文本生成失败: {str(e)}")
            yield {"error": f"文本生成失败: {str(e)}"}
            # 即使出错也发送结束信号
            yield {
                "end": True,
                "metadata": {
                    "error": str(e),
                    "processing_time": time.time() - start_time
                }
            }


# 通用 知识库上传逻辑
class DefaultDatasetUploadAction(Action):
    """文档上传动作"""

    def __init__(self, kb_handler: KnowledgeBaseHandler):
        self.kb_handler = kb_handler

    async def execute(self, context: Dict[str, Any]) -> Tuple[ActionResult, Dict[str, Any]]:
        # document_data = context.get("document_data", {})
        # user_profile = context.get("user_profile", {})

        try:
            # LogUtils.info(f"执行文档上传动作: {document_data.get('file_name', 'unknown')}")
            # start_time = time.time()
            #
            # # TODO 提取文档分段
            # document_segments = await self.kb_handler.extract_document_segment(document_data)
            #
            # # 这里可以添加更多处理逻辑，如保存到数据库等
            #
            # end_time = time.time()
            # processing_time = end_time - start_time
            #
            # context["document_segments"] = document_segments
            # context["document_id"] = document_data.get("id", "")  # 假设document_data中包含id
            # context["processing_time"] = context.get("processing_time", 0) + processing_time
            # context["user"] = user_profile.get("role", "None")
            #
            # LogUtils.info(f"文档上传处理完成，耗时 {processing_time:.2f}s")
            return ActionResult.SUCCESS, context
        except Exception as e:
            LogUtils.error(f"文档上传处理失败: {str(e)}")
            context["error"] = f"文档上传处理失败: {str(e)}"
            return ActionResult.FAILURE, context


class SecretarialDatasetUploadAction(Action):
    """文秘办公文档上传动作"""

    def __init__(self, kb_handler: KnowledgeBaseHandler, db: Session):
        self.kb_handler = kb_handler
        self.db = db

    async def execute(self, context: Dict[str, Any]) -> Tuple[ActionResult, Dict[str, Any]]:
        document_data = context.get("document_data", {})
        user_profile = context.get("user_profile", {})
        local_dataset: Dataset | None = context.get("local_dataset", None)

        LogUtils.info(f"文秘办公-> 执行文档上传动作: {document_data.get('file_name', 'unknown')}")

        try:
            start_time = time.time()
            user_id = str(user_profile.get("id", ""))
            existing_features = {}

            # 查询现有特征
            if local_dataset:
                existing_features = _get_existing_features(local_dataset.id,self.db)

            # 调用Dify特征提取接口
            dify_result = await self.kb_handler.extract_feature(
                document_data=document_data,
                existing_features=existing_features
            )

            end_time = time.time()
            processing_time = end_time - start_time

            # 新增空值校验
            if not dify_result :
                error_msg = "知识库上传解析异常：特征提取结果为空"
                LogUtils.error(error_msg)
                raise ValueError(error_msg)
            # 更新上下文
            context.update({
                "processing_time": context.get("processing_time", 0) + processing_time,
                "user": user_id,
                "features": dify_result.get('features',{})
            })

            LogUtils.info(f"文秘特征处理完成，耗时 {processing_time:.2f}s")
            return ActionResult.SUCCESS, context
        except Exception as e:
            LogUtils.error(f"文秘特征处理失败: {str(e)}")
            context["error"] = f"特征处理失败: {str(e)}"
            return ActionResult.FAILURE, context
