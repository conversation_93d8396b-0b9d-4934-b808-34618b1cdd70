import re
from typing import Dict, List

import aiohttp
import requests

from app.config import settings
from app.db.database import SessionLocal
from app.log.log_utils import LogUtils
from app.models.police_record import PoliceRecord
from app.prompts.prompts import POLICE_RECORD_ANALYSIS_PROMPT3

color_mapping = {
    '有争议需关注': 'blue',
    '有案不立': 'red',
    '降格处理': 'red',
    '待回访案件': 'yellow',
    '无问题': 'white'
}


class LLMService:
    def process_record(record: Dict) -> Dict:
        content = f'''报警内容：
                        {record.alarm_content}
                        处警详细情况：
                        {record.alarm_details}
                        处警结果：
                        {record.police_results}
                        '''

        # 提示词存放在单独的app/prompts.py文件
        if record.case_type == 0:
            promptTemplate = POLICE_RECORD_ANALYSIS_PROMPT3
            LLM_API_TOKEN = settings.LLM_CRIMINAL_API_TOKEN
        else:
            promptTemplate = POLICE_RECORD_ANALYSIS_PROMPT3
            LLM_API_TOKEN = settings.LLM_ADMINISTRATIVE_API_TOKEN

        prompt = promptTemplate.replace("content", content)
        # 获取原始请求的数据
        data = {
            "inputs": {},
            "query": prompt,
            "response_mode": "blocking",
            "conversation_id": "",
            "user": "abc-123"
        }
        headers = {
            "Authorization": f"Bearer {LLM_API_TOKEN}",
        }
        # 发送POST请求到目标API
        response = requests.post(settings.LLM_API_URL, json=data, headers=headers)

        # 如果目标API返回非200状态码，抛出异常
        response.raise_for_status()

        # 将目标API的响应结果返回给调用方
        result = response.json()
        answer = result['answer']
        # think_content = re.search(r'<think>(.*?)</think>', answer, re.DOTALL).group(1).strip()
        think_content = re.search(r'"thinking"\s*:\s*"([^"]+)"', answer).group(1)
        # data = re.search(r'"result":"(.*?)"', answer)
        # print(data)
        result_value = re.search(r'"result"\s*:\s*"([^"]+)"', answer).group(1)
        record.analysis_level = color_mapping.get(result_value, 'white')
        record.analysis_details = think_content
        record.check_results = result_value
        LogUtils.info(f"敬请分析完成PoliceRecord.id: {record.id}")
        return record

    @staticmethod
    async def do_nl2sql_process(input: str) -> str | None:
        try:
            # 获取原始请求的数据
            data = {
                "inputs": {},
                "query": input,
                "response_mode": "blocking",
                "conversation_id": "",
                "user": "abc-123"
            }
            headers = {
                "Authorization": f"Bearer {settings.LLM_API_TOKEN}",
            }

            # 使用aiohttp发送异步请求
            async with aiohttp.ClientSession() as session:
                async with session.post(settings.LLM_API_URL, json=data, headers=headers) as response:
                    # 检查响应状态
                    response.raise_for_status()
                    # 获取响应JSON
                    result = await response.json()
                    answer = result['answer']
                    LogUtils.info(f"nl2sql_process result: {answer}")

                    # 使用正则表达式匹配三个反引号包裹的内容
                    matches = re.findall(r'```sql(.*?)```', answer, re.DOTALL)

                    if matches:
                        sql_content = matches[0].strip()
                    return sql_content
        except Exception as e:
            LogUtils.error(f"LLM处理失败: {str(e)}")
            return None

    # @staticmethod
    def process_batch(records: List[Dict]):
        db = SessionLocal()
        try:
            for record in records:
                try:
                    record = db.query(PoliceRecord).filter_by(id=record.id).first()
                    record.analysis_status = 1
                    record = db.merge(record)
                    db.commit()
                    # 单独处理每条记录，如果失败不影响其他记录
                    record = LLMService.process_record(record)
                    record.analysis_status = 2
                    db.merge(record)
                    db.commit()

                except Exception as e:
                    # 记录日志
                    LogUtils.error(f"处理记录police_number={record.police_number} 失败: {str(e)}")
                    # 确保当前记录状态为失败
                    record.analysis_status = 3
                    record.analysis_details = str(e)
                    db.merge(record)
                    db.commit()
                    # 继续处理下一条记录
                    continue
        except Exception as e:
            LogUtils.error(f"process_batch整体处理失败: {str(e)}")
            db.rollback()
            raise
        finally:
            db.close()

    def model_filed_check(check_result: Dict) -> Dict:
        # 处理各个字段并生成结果
        if check_result.get('check_status') is None:
            flag = 0
        else:
            flag = check_result.get('check_status')
        # check_result.get('check_status') 如果存在则判断 得到的结果中的 status 对应的值
        blnr_chart = check_result.get('blnr_chart')
        if blnr_chart and blnr_chart.get('status') and blnr_chart.get('status') == 'ERROR':
            flag = 1
        blnr_punctuation = check_result.get('blnr_punctuation')
        if blnr_punctuation and blnr_punctuation.get('status') and blnr_punctuation.get('status') == 'ERROR':
            flag = 1
        check_result['check_status'] = flag
        return check_result
