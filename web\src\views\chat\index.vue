<template>
  <div class="chat-container">
    <chat-header title="通用对话助手" />
    <div class="chat-wrapper">
      <div class="chat-main">
        <div class="chat-content">
          <div class="welcome-title">你好，我是通用对话助手，你可以立即跟我聊天。</div>
          <template v-if="!currentConversationId">
            <div class="chat-footer-center">
              <chat-input namespace="chat" :isShowKnowledge="false" />
            </div>
          </template>
          <template v-else>
            <chat-history namespace="chat" />
            <div class="chat-footer">
              <chat-input namespace="chat" :isShowKnowledge="false" />
            </div>
          </template>
        </div>
      </div>
      <div class="chat-sidebar">
        <chat-list title="通用对话助手" tipTitle="DeepSeek通用智能体" namespace="chat" />
      </div>
    </div>
  </div>
</template>

<script>
import ChatList from "@/components/ChatList.vue";
import ChatHistory from "@/components/ChatHistory.vue";
import ChatInput from "@/components/ChatInput.vue";
import ChatHeader from "@/components/ChatHeader.vue";
import { mapGetters } from "vuex";

export default {
  name: "ChatView",
  components: {
    ChatList,
    ChatHistory,
    ChatInput,
    ChatHeader,
  },
  computed: {
    ...mapGetters("chat", ["currentConversationId"]),
  },
  created() {},
};
</script>
<style scoped lang="less">
.chat-container {
  display: flex;
  height: 100vh;
  /* background: #f5f7fa; */
  background: #071827;
  flex-direction: column;
}
.chat-wrapper {
  display: flex;
  flex: 1;
  height: calc(100vh - 95px);
}
.chat-sidebar {
  // width: 260px;
  /* background: white; */

  border-left: 1px solid #06445d;
  box-shadow: #06445d 0px 0px 10px 0px;
  display: flex;
  flex-direction: column;
}

.chat-main {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  // position: relative;
  /* background: #0F2336; */
  /* border-radius: 8px; */
  /* box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1); */
  .welcome-title {
    border-radius: 20px;
    width: 80%;
    margin: 0 auto;
    text-align: center;
    // margin-top: 350px;
    position: absolute;
    top: 15px;
    left: 50%;
    transform: translateX(-50%);

    font-size: 18px;
    padding: 5px;
    color: rgba(255, 255, 255, 0.7);
  }

  .chat-footer-center {
    margin-top: 350px;
  }
}

.chat-header {
  padding: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.chat-header h2 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.chat-content {
  flex: 1;
  height: calc(100vh - 65px);
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 0;
  padding-top: 60px;
}

.chat-footer {
  /* border-top: 1px solid #e4e7ed; */
  /* background: white; */
  flex-shrink: 0;
  height: fit-content;
}
</style>
